package com.huazheng.dmhg.service;

import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.IService;
import com.huazheng.dmhg.common.R;
import com.huazheng.dmhg.entity.InspectionTask;


import java.util.List;

/**
 * 巡检任务表 Service
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
public interface InspectionTaskService extends IService<InspectionTask> {

    /**
     * 巡检任务分页查询
     *
     * @param inspectionTask 查询条件
     * @return 分页结果
     * <AUTHOR>
     * @since 2025-08-20
     */
    Page<InspectionTask> page(InspectionTask inspectionTask);

    /**
     * 巡检任务详情
     *
     * @param taskId 任务ID
     * @return 任务详情
     * <AUTHOR>
     * @since 2025-08-20
     */
    R<InspectionTask> info(Long taskId);

    /**
     * 保存巡检任务
     *
     * @param inspectionTask 巡检任务
     * @return 保存结果
     * <AUTHOR>
     * @since 2025-08-20
     */
    R<InspectionTask> save(InspectionTask inspectionTask);

    /**
     * 修改巡检任务
     *
     * @param inspectionTask 巡检任务
     * @return 修改结果
     * <AUTHOR>
     * @since 2025-08-20
     */
    R<Void> update(InspectionTask inspectionTask);

    /**
     * 批量删除巡检任务
     *
     * @param taskIds 任务ID数组
     * @return 删除结果
     * <AUTHOR>
     * @since 2025-08-20
     */
    R<Void> delete(Long[] taskIds);

    /**
     * 巡检任务列表查询
     *
     * @param inspectionTask 查询条件
     * @return 任务列表
     * <AUTHOR>
     * @since 2025-08-20
     */
    R<List<InspectionTask>> list(InspectionTask inspectionTask);

}
