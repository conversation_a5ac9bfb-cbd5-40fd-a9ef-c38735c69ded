<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.huazheng.dmhg.mapper.AlarmEventMapper">
    <!-- 通用返回对象 -->
    <resultMap type="com.huazheng.dmhg.entity.AlarmEvent" id="alarmEventResult">
        <result property="alertId" column="alert_id"/> <!-- 告警ID -->
        <result property="alertCategory" column="alert_category"/> <!-- 告警类别 -->
        <result property="deviceId" column="device_id"/> <!-- 上报设备ID -->
        <result property="deviceName" column="device_name"/> <!-- 上报设备名称 -->
        <result property="reportTime" column="report_time"/> <!-- 上报时间 -->
        <result property="alertImage" column="alert_image"/> <!-- 告警图片 -->
        <result property="extendInfo" column="extend_info"/> <!-- 告警扩展信息 -->
        <result property="eventStatus" column="event_status"/> <!-- 事件状态 -->
        <result property="handler" column="handler"/> <!-- 处理人 -->
        <result property="handleTime" column="handle_time"/> <!-- 处理时间 -->
        <result property="handleRemark" column="handle_remark"/> <!-- 处置说明 -->
        <result property="delFlag" column="del_flag"/> <!-- 删除标志 -->
        <result property="remark" column="remark"/> <!-- 备注 -->
        <result property="createBy" column="create_by"/> <!-- 创建人ID -->
        <result property="createName" column="create_name"/> <!-- 创建人姓名 -->
        <result property="createTime" column="create_time"/> <!-- 创建时间 -->
        <result property="updateBy" column="update_by"/> <!-- 修改人ID -->
        <result property="updateName" column="update_name"/> <!-- 修改人 -->
        <result property="updateTime" column="update_time"/> <!-- 修改时间 -->
    </resultMap>

    <sql id="selectAlarmEventVo">
        select alert_id,
               alert_category,
               device_id,
               device_name,
               report_time,
               alert_image,
               extend_info,
               event_status,
               handler,
               handle_time,
               handle_remark,
               del_flag,
               remark,
               create_by,
               create_name,
               create_time,
               update_by,
               update_name,
               update_time
        from alarm_event
    </sql>

    <!-- 模糊查询条件 -->
    <sql id="like">
        <if test="alertCategory != null and alertCategory != ''">
            AND alert_category = #{alertCategory}
        </if>
        <if test="deviceId != null and deviceId != ''">
            AND device_id = #{deviceId}
        </if>
        <if test="deviceName != null and deviceName != ''">
            AND device_name like concat('%', #{deviceName}, '%')
        </if>
        <if test="eventStatus != null and eventStatus != ''">
            AND event_status = #{eventStatus}
        </if>
        <if test="handler != null and handler != ''">
            AND handler like concat('%', #{handler}, '%')
        </if>
        <if test="queryStartTime != null and queryStartTime != ''">
            AND report_time >= #{queryStartTime}
        </if>
        <if test="queryEndTime != null and queryEndTime != ''">
            AND report_time &lt;= #{queryEndTime}
        </if>
        AND del_flag = 'N'
    </sql>

    <!-- 分页查询告警记录 -->
    <select id="pageAlarmEvent" parameterType="AlarmEvent" resultMap="alarmEventResult">
        <include refid="selectAlarmEventVo"/>
        <where>
            <include refid="like"/>
        </where>
        ORDER BY
            CASE WHEN event_status = '待处理' THEN 0 ELSE 1 END,
            report_time DESC
    </select>

    <!-- 根据主键查询对象 -->
    <select id="infoAlarmEvent" parameterType="Long" resultMap="alarmEventResult">
        <include refid="selectAlarmEventVo"/>
        WHERE alert_id = #{alertId} AND del_flag = 'N'
    </select>

    <!-- 保存告警记录 -->
    <insert id="saveAlarmEvent" parameterType="AlarmEvent" useGeneratedKeys="true" keyProperty="alertId">
        INSERT INTO alarm_event
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="alertCategory != null and alertCategory != ''">alert_category,</if>
            <if test="deviceId != null and deviceId != ''">device_id,</if>
            <if test="deviceName != null and deviceName != ''">device_name,</if>
            <if test="reportTime != null">report_time,</if>
            <if test="alertImage != null and alertImage != ''">alert_image,</if>
            <if test="extendInfo != null and extendInfo != ''">extend_info,</if>
            <if test="eventStatus != null and eventStatus != ''">event_status,</if>
            <if test="handler != null and handler != ''">handler,</if>
            <if test="handleTime != null">handle_time,</if>
            <if test="handleRemark != null and handleRemark != ''">handle_remark,</if>
            <if test="delFlag != null and delFlag != ''">del_flag,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createName != null and createName != ''">create_name,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="alertCategory != null and alertCategory != ''">#{alertCategory},</if>
            <if test="deviceId != null and deviceId != ''">#{deviceId},</if>
            <if test="deviceName != null and deviceName != ''">#{deviceName},</if>
            <if test="reportTime != null">#{reportTime},</if>
            <if test="alertImage != null and alertImage != ''">#{alertImage},</if>
            <if test="extendInfo != null and extendInfo != ''">#{extendInfo},</if>
            <if test="eventStatus != null and eventStatus != ''">#{eventStatus},</if>
            <if test="handler != null and handler != ''">#{handler},</if>
            <if test="handleTime != null">#{handleTime},</if>
            <if test="handleRemark != null and handleRemark != ''">#{handleRemark},</if>
            <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createName != null and createName != ''">#{createName},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

    <!-- 修改告警记录 -->
    <update id="updateAlarmEvent" parameterType="AlarmEvent">
        UPDATE alarm_event
        <trim prefix="SET" suffixOverrides=",">
            <if test="alertCategory != null and alertCategory != ''">alert_category = #{alertCategory},</if>
            <if test="deviceId != null and deviceId != ''">device_id = #{deviceId},</if>
            <if test="deviceName != null and deviceName != ''">device_name = #{deviceName},</if>
            <if test="reportTime != null">report_time = #{reportTime},</if>
            <if test="alertImage != null">alert_image = #{alertImage},</if>
            <if test="extendInfo != null">extend_info = #{extendInfo},</if>
            <if test="eventStatus != null and eventStatus != ''">event_status = #{eventStatus},</if>
            <if test="handler != null">handler = #{handler},</if>
            <if test="handleTime != null">handle_time = #{handleTime},</if>
            <if test="handleRemark != null">handle_remark = #{handleRemark},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateName != null and updateName != ''">update_name = #{updateName},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        WHERE alert_id = #{alertId} AND del_flag = 'N'
    </update>

    <!-- 批量删除告警记录（逻辑删除） -->
    <update id="deleteAlarmEvent">
        UPDATE alarm_event SET del_flag = 'Y', update_by = #{updateBy}, update_time = NOW()
        WHERE alert_id IN
        <foreach item="alertId" collection="alertIds" open="(" separator="," close=")">
            #{alertId}
        </foreach>
        AND del_flag = 'N'
    </update>

    <!-- 查询告警记录列表 -->
    <select id="listAlarmEvent" parameterType="AlarmEvent" resultMap="alarmEventResult">
        <include refid="selectAlarmEventVo"/>
        <where>
            <include refid="like"/>
        </where>
        ORDER BY
            CASE WHEN event_status = '待处理' THEN 0 ELSE 1 END,
            create_time DESC
    </select>

</mapper>
