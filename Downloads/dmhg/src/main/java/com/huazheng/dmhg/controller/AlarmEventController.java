package com.huazheng.dmhg.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huazheng.dmhg.common.R;
import com.huazheng.dmhg.entity.AlarmEvent;
import com.huazheng.dmhg.dto.AlarmEventReceiveDTO;
import com.huazheng.dmhg.service.AlarmEventService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 告警记录表 Controller
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
@RestController
@RequestMapping("alarmEvent")
public class AlarmEventController {

    @Autowired
    private AlarmEventService alarmEventService;

    /**
     * 告警记录分页查询
     *
     * @param alarmEvent 查询参数
     * @return 分页结果
     * <AUTHOR>
     * @since 2025-08-20
     */
    @GetMapping("/page")
    public R<Page<AlarmEvent>> page(AlarmEvent alarmEvent) {
        return R.success(alarmEventService.page(alarmEvent));
    }

    /**
     * 告警记录详情
     *
     * @param alertId 告警ID
     * @return 告警详情
     * <AUTHOR>
     * @since 2025-08-20
     */
    @GetMapping("/{alertId}")
    public R<AlarmEvent> info(@PathVariable("alertId") Long alertId) {
        return alarmEventService.info(alertId);
    }

    /**
     * 保存告警记录
     *
     * @param alarmEvent 告警记录
     * @return 保存结果
     * <AUTHOR>
     * @since 2025-08-20
     */
    @PostMapping("/save")
    public R<AlarmEvent> saveAlarmEvent(@RequestBody AlarmEvent alarmEvent) {
        return alarmEventService.saveAlarmEvent(alarmEvent);
    }

    /**
     * 修改告警记录
     *
     * @param alarmEvent 告警记录
     * @return 修改结果
     * <AUTHOR>
     * @since 2025-08-20
     */
    @PostMapping("/renew")
    public R<Void> update(@RequestBody AlarmEvent alarmEvent) {
        return alarmEventService.update(alarmEvent);
    }

    /**
     * 批量删除告警记录
     *
     * @param alertIds 删除的告警ID数组
     * @return 删除结果
     * <AUTHOR>
     * @since 2025-08-20
     */
    @PostMapping("/del/{alertIds}")
    public R<Void> delete(@PathVariable Long[] alertIds) {
        return alarmEventService.delete(alertIds);
    }

    /**
     * 告警记录列表查询
     *
     * @param alarmEvent 查询参数
     * @return 告警列表
     * <AUTHOR>
     * @since 2025-08-20
     */
    @GetMapping("/list")
    public R<List<AlarmEvent>> list(AlarmEvent alarmEvent) {
        return alarmEventService.list(alarmEvent);
    }

    /**
     * 接收对接方告警事件数据
     *
     * @param receiveDTO 对接方告警事件数据
     * @return 保存结果
     * <AUTHOR>
     * @since 2025-08-20
     */
    @PostMapping("/receive")
    public R<AlarmEvent> receiveAlarmEvent(@RequestBody AlarmEventReceiveDTO receiveDTO) {
        return alarmEventService.receiveAlarmEvent(receiveDTO);
    }


    /**
     *  事件处理接口
     *
     * @param alarmEvent 事件处理参数
     * @return 处理结果
     * <AUTHOR>
     * @since 2025-08-20
     */
    @PostMapping("/handle")
    public R handle(@RequestBody AlarmEvent alarmEvent) {
        return alarmEventService.handle(alarmEvent);
    }


}
