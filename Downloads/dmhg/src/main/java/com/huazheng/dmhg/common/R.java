package com.huazheng.dmhg.common;

import lombok.Data;

import java.io.Serializable;

/**
 * 统一返回结果类
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
@Data
public class R<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 成功标识
     */
    private static final int SUCCESS = 200;

    /**
     * 失败标识
     */
    private static final int FAIL = 500;

    /**
     * 返回标识
     */
    private int code;

    /**
     * 返回信息
     */
    private String msg;

    /**
     * 数据
     */
    private T data;

    public static <T> R<T> success() {
        return restResult(null, SUCCESS, "操作成功");
    }

    public static <T> R<T> success(T data) {
        return restResult(data, SUCCESS, "操作成功");
    }

    public static <T> R<T> success(T data, String msg) {
        return restResult(data, SUCCESS, msg);
    }

    public static <T> R<T> error() {
        return restResult(null, FAIL, "操作失败");
    }

    public static <T> R<T> error(String msg) {
        return restResult(null, FAIL, msg);
    }

    public static <T> R<T> error(T data) {
        return restResult(data, FAIL, "操作失败");
    }

    public static <T> R<T> error(T data, String msg) {
        return restResult(data, FAIL, msg);
    }

    public static <T> R<T> error(int code, String msg) {
        return restResult(null, code, msg);
    }

    private static <T> R<T> restResult(T data, int code, String msg) {
        R<T> apiResult = new R<>();
        apiResult.setCode(code);
        apiResult.setData(data);
        apiResult.setMsg(msg);
        return apiResult;
    }

    public boolean isSuccess() {
        return SUCCESS == this.code;
    }

    public boolean isError() {
        return !isSuccess();
    }
}
