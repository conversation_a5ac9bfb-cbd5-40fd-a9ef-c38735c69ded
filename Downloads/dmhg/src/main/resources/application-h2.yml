server:
  port: 10000
  servlet:
    context-path: /dmhg-smarkpark

spring:
  application:
    name: dmhg-smarkpark-standalone
  
  # H2数据源配置（用于测试）
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
    h2:
      console:
        enabled: true
        path: /h2-console

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false

# MyBatis Plus配置
mybatis-plus:
  mapper-locations: classpath*:mapper/*.xml
  type-aliases-package: com.huazheng.dmhg.entity
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: delFlag
      logic-delete-value: Y
      logic-not-delete-value: N
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'

# 日志配置
logging:
  level:
    com.huazheng.dmhg.mapper: debug
    org.springframework.web: info
    root: info
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n'
