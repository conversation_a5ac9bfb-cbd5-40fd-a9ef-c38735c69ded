server:
  port: 10000
  servlet:
    context-path: /dmhg-smarkpark

spring:
  application:
    name: dmhg-smarkpark-standalone
  
  # 数据源配置
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.jdbc.Driver
    url: ************************************************************************************************************************************************************************************************
    username: admin
    password: Tunny_huazheng2025*
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: DatebookHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1
      validation-timeout: 3000
      leak-detection-threshold: 60000

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false

# MyBatis Plus配置
mybatis-plus:
  mapper-locations: classpath*:mapper/*.xml
  type-aliases-package: com.huazheng.dmhg.entity
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: delFlag
      logic-delete-value: Y
      logic-not-delete-value: N
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'

# 日志配置
logging:
  level:
    com.huazheng.dmhg.mapper: debug
    org.springframework.web: info
    root: info
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n'
    file: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n'
  file:
    name: logs/dmhg-smarkpark.log
    max-size: 100MB
    max-history: 30

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      show-details: when-authorized
