package com.huazheng.dmhg.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.huazheng.dmhg.common.R;
import com.huazheng.dmhg.entity.AlarmEvent;
import com.huazheng.dmhg.dto.AlarmEventReceiveDTO;

import java.util.List;

/**
 * 告警记录表 Service
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
public interface AlarmEventService extends IService<AlarmEvent> {

    /**
     * 告警记录分页查询
     *
     * @param alarmEvent 查询条件
     * @return 分页结果
     * <AUTHOR>
     * @since 2025-08-20
     */
    Page<AlarmEvent> page(AlarmEvent alarmEvent);

    /**
     * 告警记录详情
     *
     * @param alertId 告警ID
     * @return 告警详情
     * <AUTHOR>
     * @since 2025-08-20
     */
    R<AlarmEvent> info(Long alertId);

    /**
     * 保存告警记录
     *
     * @param alarmEvent 告警记录
     * @return 保存结果
     * <AUTHOR>
     * @since 2025-08-20
     */
    R saveAlarmEvent(AlarmEvent alarmEvent);

    /**
     * 修改告警记录
     *
     * @param alarmEvent 告警记录
     * @return 修改结果
     * <AUTHOR>
     * @since 2025-08-20
     */
    R<Void> update(AlarmEvent alarmEvent);

    /**
     * 批量删除告警记录
     *
     * @param alertIds 告警ID数组
     * @return 删除结果
     * <AUTHOR>
     * @since 2025-08-20
     */
    R<Void> delete(Long[] alertIds);

    /**
     * 告警记录列表查询
     *
     * @param alarmEvent 查询条件
     * @return 告警列表
     * <AUTHOR>
     * @since 2025-08-20
     */
    R<List<AlarmEvent>> list(AlarmEvent alarmEvent);

    /**
     * 接收对接方告警事件数据
     *
     * @param receiveDTO 对接方告警事件数据
     * @return 保存结果
     * <AUTHOR>
     * @since 2025-08-20
     */
    R<AlarmEvent> receiveAlarmEvent(AlarmEventReceiveDTO receiveDTO);

    R handle(AlarmEvent alarmEvent);
}
