package com.huazheng.dmhg.service.impl;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huazheng.dmhg.common.R;
import com.huazheng.dmhg.entity.InspectionTask;
import com.huazheng.dmhg.mapper.InspectionTaskMapper;
import com.huazheng.dmhg.service.InspectionTaskService;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 巡检任务表 Service 实现类
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
@Slf4j
@Service
public class InspectionTaskServiceImpl extends ServiceImpl<InspectionTaskMapper, InspectionTask> implements InspectionTaskService {

    @Autowired
    private InspectionTaskMapper inspectionTaskMapper;

    /**
     * 巡检任务分页查询
     *
     * @param inspectionTask 查询条件
     * @return 分页结果
     * <AUTHOR>
     * @since 2025-08-20
     */
    @Override
    public Page<InspectionTask> page(InspectionTask inspectionTask) {
        Page<InspectionTask> page = new Page<>(inspectionTask.getPage() == null ? 1 : inspectionTask.getPage(),
                inspectionTask.getLimit() == null ? 10 : inspectionTask.getLimit());
        List<InspectionTask> records = inspectionTaskMapper.pageInspectionTask(page, inspectionTask);
        page.setRecords(records);
        return page;
    }

    /**
     * 巡检任务详情
     *
     * @param taskId 任务ID
     * @return 任务详情
     * <AUTHOR>
     * @since 2025-08-20
     */
    @Override
    public R<InspectionTask> info(Long taskId) {
        InspectionTask inspectionTask = inspectionTaskMapper.infoInspectionTask(taskId);
        if (inspectionTask == null) {
            return R.error("巡检任务不存在");
        }
        return R.success(inspectionTask);
    }

    /**
     * 保存巡检任务
     *
     * @param inspectionTask 巡检任务
     * @return 保存结果
     * <AUTHOR>
     * @since 2025-08-20
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R saveInspectionTask(InspectionTask inspectionTask) {
        // 简化版本：使用默认用户信息
        inspectionTask.setCreateBy(1L);
        inspectionTask.setCreateName("系统管理员");
        inspectionTask.setCreateTime(LocalDateTime.now());
        inspectionTask.setDelFlag("N");
        
        int result = inspectionTaskMapper.saveInspectionTask(inspectionTask);
        if (result > 0) {
            return R.success(inspectionTask);
        }
        return R.error("保存失败");
    }

    /**
     * 修改巡检任务
     *
     * @param inspectionTask 巡检任务
     * @return 修改结果
     * <AUTHOR>
     * @since 2025-08-20
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Void> update(InspectionTask inspectionTask) {
        // 简化版本：使用默认用户信息
        inspectionTask.setUpdateBy(1L);
        inspectionTask.setUpdateName("系统管理员");
        inspectionTask.setUpdateTime(LocalDateTime.now());
        
        int result = inspectionTaskMapper.updateInspectionTask(inspectionTask);
        if (result > 0) {
            return R.success();
        }
        return R.error("修改失败");
    }

    /**
     * 批量删除巡检任务
     *
     * @param taskIds 任务ID数组
     * @return 删除结果
     * <AUTHOR>
     * @since 2025-08-20
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Void> delete(Long[] taskIds) {
        // 简化版本：使用默认用户信息
        int result = inspectionTaskMapper.deleteInspectionTask(taskIds, 1L);
        if (result > 0) {
            return R.success();
        }
        return R.error("删除失败");
    }

    /**
     * 巡检任务列表查询
     *
     * @param inspectionTask 查询条件
     * @return 任务列表
     * <AUTHOR>
     * @since 2025-08-20
     */
    @Override
    public R<List<InspectionTask>> list(InspectionTask inspectionTask) {
        List<InspectionTask> list = inspectionTaskMapper.listInspectionTask(inspectionTask);
        return R.success(list);
    }

}
