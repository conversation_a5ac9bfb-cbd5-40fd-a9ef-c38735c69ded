package com.huazheng.dmhg.mapper;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.dmhg.entity.InspectionTask;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 巡检任务表 Mapper
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
@Component
public interface InspectionTaskMapper extends BaseMapper<InspectionTask> {

    /**
     * 分页查询巡检任务
     *
     * @param page  分页参数
     * @param param 查询参数
     * @return 巡检任务列表
     * <AUTHOR>
     * @since 2025-08-20
     */
    List<InspectionTask> pageInspectionTask(Page<InspectionTask> page, InspectionTask param);

    /**
     * 查询巡检任务详情
     *
     * @param taskId 任务ID
     * @return 巡检任务详情
     * <AUTHOR>
     * @since 2025-08-20
     */
    InspectionTask infoInspectionTask(Long taskId);

    /**
     * 保存巡检任务
     *
     * @param param 巡检任务参数
     * @return 影响行数
     * <AUTHOR>
     * @since 2025-08-20
     */
    int saveInspectionTask(InspectionTask param);

    /**
     * 修改巡检任务
     *
     * @param param 巡检任务参数
     * @return 影响行数
     * <AUTHOR>
     * @since 2025-08-20
     */
    int updateInspectionTask(InspectionTask param);

    /**
     * 批量删除巡检任务
     *
     * @param taskIds  任务ID数组
     * @param updateBy 修改人ID
     * @return 影响行数
     * <AUTHOR>
     * @since 2025-08-20
     */
    int deleteInspectionTask(@Param("taskIds") Long[] taskIds, @Param("updateBy") Long updateBy);

    /**
     * 查询巡检任务列表
     *
     * @param inspectionTask 查询参数
     * @return 巡检任务列表
     * <AUTHOR>
     * @since 2025-08-20
     */
    List<InspectionTask> listInspectionTask(InspectionTask inspectionTask);

}
