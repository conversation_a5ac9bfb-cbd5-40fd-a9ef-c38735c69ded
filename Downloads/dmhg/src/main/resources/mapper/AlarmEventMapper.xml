<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huazheng.dmhg.mapper.AlarmEventMapper">

    <!-- 基础字段映射 -->
    <sql id="Base_Column_List">
        alert_id, alert_category, device_id, device_name, report_time, alert_image, 
        extend_info, event_status, handler, handle_time, handle_remark, del_flag, 
        remark, create_time, update_time, create_by, create_name, update_by, update_name
    </sql>

    <!-- 分页查询告警记录 -->
    <select id="pageAlarmEvent" resultType="com.huazheng.dmhg.entity.AlarmEvent">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM alarm_event
        <where>
            del_flag = 'N'
            <if test="param.alertCategory != null and param.alertCategory != ''">
                AND alert_category LIKE CONCAT('%', #{param.alertCategory}, '%')
            </if>
            <if test="param.deviceId != null and param.deviceId != ''">
                AND device_id LIKE CONCAT('%', #{param.deviceId}, '%')
            </if>
            <if test="param.deviceName != null and param.deviceName != ''">
                AND device_name LIKE CONCAT('%', #{param.deviceName}, '%')
            </if>
            <if test="param.eventStatus != null and param.eventStatus != ''">
                AND event_status = #{param.eventStatus}
            </if>
            <if test="param.queryStartTime != null and param.queryStartTime != ''">
                AND report_time >= #{param.queryStartTime}
            </if>
            <if test="param.queryEndTime != null and param.queryEndTime != ''">
                AND report_time &lt;= #{param.queryEndTime}
            </if>
        </where>
        ORDER BY report_time DESC
    </select>

    <!-- 查询告警记录详情 -->
    <select id="infoAlarmEvent" resultType="com.huazheng.dmhg.entity.AlarmEvent">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM alarm_event
        WHERE alert_id = #{alertId} AND del_flag = 'N'
    </select>

    <!-- 保存告警记录 -->
    <insert id="saveAlarmEvent" useGeneratedKeys="true" keyProperty="alertId">
        INSERT INTO alarm_event (
            alert_category, device_id, device_name, report_time, alert_image, 
            extend_info, event_status, handler, handle_time, handle_remark, 
            del_flag, remark, create_time, update_time, create_by, create_name, 
            update_by, update_name
        ) VALUES (
            #{alertCategory}, #{deviceId}, #{deviceName}, #{reportTime}, #{alertImage}, 
            #{extendInfo}, #{eventStatus}, #{handler}, #{handleTime}, #{handleRemark}, 
            #{delFlag}, #{remark}, #{createTime}, #{updateTime}, #{createBy}, #{createName}, 
            #{updateBy}, #{updateName}
        )
    </insert>

    <!-- 修改告警记录 -->
    <update id="updateAlarmEvent">
        UPDATE alarm_event
        <set>
            <if test="alertCategory != null">alert_category = #{alertCategory},</if>
            <if test="deviceId != null">device_id = #{deviceId},</if>
            <if test="deviceName != null">device_name = #{deviceName},</if>
            <if test="reportTime != null">report_time = #{reportTime},</if>
            <if test="alertImage != null">alert_image = #{alertImage},</if>
            <if test="extendInfo != null">extend_info = #{extendInfo},</if>
            <if test="eventStatus != null">event_status = #{eventStatus},</if>
            <if test="handler != null">handler = #{handler},</if>
            <if test="handleTime != null">handle_time = #{handleTime},</if>
            <if test="handleRemark != null">handle_remark = #{handleRemark},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateName != null">update_name = #{updateName},</if>
        </set>
        WHERE alert_id = #{alertId} AND del_flag = 'N'
    </update>

    <!-- 批量删除告警记录（逻辑删除） -->
    <update id="deleteAlarmEvent">
        UPDATE alarm_event 
        SET del_flag = 'Y', update_by = #{updateBy}, update_time = NOW()
        WHERE alert_id IN
        <foreach collection="alertIds" item="alertId" open="(" separator="," close=")">
            #{alertId}
        </foreach>
        AND del_flag = 'N'
    </update>

    <!-- 查询告警记录列表 -->
    <select id="listAlarmEvent" resultType="com.huazheng.dmhg.entity.AlarmEvent">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM alarm_event
        <where>
            del_flag = 'N'
            <if test="alertCategory != null and alertCategory != ''">
                AND alert_category LIKE CONCAT('%', #{alertCategory}, '%')
            </if>
            <if test="deviceId != null and deviceId != ''">
                AND device_id LIKE CONCAT('%', #{deviceId}, '%')
            </if>
            <if test="deviceName != null and deviceName != ''">
                AND device_name LIKE CONCAT('%', #{deviceName}, '%')
            </if>
            <if test="eventStatus != null and eventStatus != ''">
                AND event_status = #{eventStatus}
            </if>
            <if test="queryStartTime != null and queryStartTime != ''">
                AND report_time >= #{queryStartTime}
            </if>
            <if test="queryEndTime != null and queryEndTime != ''">
                AND report_time &lt;= #{queryEndTime}
            </if>
        </where>
        ORDER BY report_time DESC
    </select>

</mapper>
