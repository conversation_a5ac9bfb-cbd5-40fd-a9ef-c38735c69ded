package com.huazheng.dmhg.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.huazheng.dmhg.common.R;
import com.huazheng.dmhg.entity.InspectionTask;
import com.huazheng.dmhg.service.InspectionTaskService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 巡检任务表 Controller
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
@RestController
@RequestMapping("inspectionTask")
public class InspectionTaskController {

    @Autowired
    private InspectionTaskService inspectionTaskService;

    /**
     * 巡检任务分页查询
     *
     * @param inspectionTask 查询参数
     * @return 分页结果
     * <AUTHOR>
     * @since 2025-08-20
     */
    @GetMapping("/page")
    public R<Page<InspectionTask>> page(InspectionTask inspectionTask) {
        return R.success(inspectionTaskService.page(inspectionTask));
    }

    /**
     * 巡检任务详情
     *
     * @param taskId 任务ID
     * @return 任务详情
     * <AUTHOR>
     * @since 2025-08-20
     */
    @GetMapping("/{taskId}")
    public R<InspectionTask> info(@PathVariable("taskId") Long taskId) {
        return inspectionTaskService.info(taskId);
    }

    /**
     * 保存巡检任务
     *
     * @param inspectionTask 巡检任务
     * @return 保存结果
     * <AUTHOR>
     * @since 2025-08-20
     */
    @PostMapping("/save")
    public R<InspectionTask> save(@RequestBody InspectionTask inspectionTask) {
        return inspectionTaskService.saveInspectionTask(inspectionTask);
    }

    /**
     * 修改巡检任务
     *
     * @param inspectionTask 巡检任务
     * @return 修改结果
     * <AUTHOR>
     * @since 2025-08-20
     */
    @PostMapping("/renew")
    public R<Void> update(@RequestBody InspectionTask inspectionTask) {
        return inspectionTaskService.update(inspectionTask);
    }

    /**
     * 批量删除巡检任务
     *
     * @param taskIds 删除的任务ID数组
     * @return 删除结果
     * <AUTHOR>
     * @since 2025-08-20
     */
    @PostMapping("/del/{taskIds}")
    public R<Void> delete(@PathVariable Long[] taskIds) {
        return inspectionTaskService.delete(taskIds);
    }

    /**
     * 巡检任务列表查询
     *
     * @param inspectionTask 查询参数
     * @return 任务列表
     * <AUTHOR>
     * @since 2025-08-20
     */
    @GetMapping("/list")
    public R<List<InspectionTask>> list(InspectionTask inspectionTask) {
        return inspectionTaskService.list(inspectionTask);
    }

}
