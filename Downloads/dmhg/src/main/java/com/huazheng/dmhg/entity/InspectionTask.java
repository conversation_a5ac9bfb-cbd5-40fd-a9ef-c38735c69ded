package com.huazheng.dmhg.entity;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 巡检任务表
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("inspection_task")
public class InspectionTask extends Model<InspectionTask> {
    private static final long serialVersionUID = 1L;
    
    /**
     * 任务ID
     */
    @TableId(value = "task_id", type = IdType.AUTO)
    private Long taskId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 机器狗ID/巡更组ID/视频设备组ID
     */
    private String executeId;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 任务状态（待执行、执行中、已完成）
     */
    private String taskStatus;

    /**
     * 任务类型（机器狗巡检、人工巡检、视频巡检）
     */
    private String taskType;

    /**
     * 删除标志（N代表存在 Y代表删除）
     */
    @TableLogic(value = "N", delval = "Y")
    private String delFlag;

    /**
     * 备注,描述
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String remark;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 创建人 ID
     */
    private Long createBy;

    /**
     * 创建人姓名
     */
    private String createName;

    /**
     * 修改人 ID
     */
    private Long updateBy;

    /**
     * 修改人
     */
    private String updateName;

    @Override
    protected Serializable pkVal() {
        return this.taskId;
    }

    /**
     * 查询页码
     */
    @TableField(exist = false)
    private Integer page;

    /**
     * 每页条数
     */
    @TableField(exist = false)
    private Integer limit;

    /**
     * 查询开始时间
     */
    @TableField(exist = false)
    private String queryStartTime;

    /**
     * 查询结束时间
     */
    @TableField(exist = false)
    private String queryEndTime;
}
