package com.huazheng.dmhg.entity;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 告警记录表
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("alarm_event")
public class AlarmEvent extends Model<AlarmEvent> {
    private static final long serialVersionUID = 1L;
    
    /**
     * 告警ID
     */
    @TableId(value = "alert_id", type = IdType.AUTO)
    private Long alertId;

    /**
     * 告警类别（设备故障、入侵检测、火灾报警、异常行为等）
     */
    private String alertCategory;

    /**
     * 上报设备ID
     */
    private String deviceId;

    /**
     * 上报设备名称
     */
    private String deviceName;

    /**
     * 上报时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime reportTime;

    /**
     * 告警图片（BASE64格式）
     */
    private String alertImage;

    /**
     * 告警扩展信息（JSON格式）
     */
    private String extendInfo;

    /**
     * 事件状态（待处理、已处理、已关闭）
     */
    private String eventStatus;

    /**
     * 处理人
     */
    private String handler;

    /**
     * 处理时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime handleTime;

    /**
     * 处置说明
     */
    private String handleRemark;

    /**
     * 删除标志（N代表存在 Y代表删除）
     */
    @TableLogic(value = "N", delval = "Y")
    private String delFlag;

    /**
     * 备注,描述
     */
    @TableField(strategy = FieldStrategy.IGNORED)
    private String remark;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 创建人 ID
     */
    private Long createBy;

    /**
     * 创建人姓名
     */
    private String createName;

    /**
     * 修改人 ID
     */
    private Long updateBy;

    /**
     * 修改人
     */
    private String updateName;

    @Override
    protected Serializable pkVal() {
        return this.alertId;
    }

    /**
     * 查询页码
     */
    @TableField(exist = false)
    private Integer page;

    /**
     * 每页条数
     */
    @TableField(exist = false)
    private Integer limit;

    /**
     * 查询开始时间
     */
    @TableField(exist = false)
    private String queryStartTime;

    /**
     * 查询结束时间
     */
    @TableField(exist = false)
    private String queryEndTime;
}
