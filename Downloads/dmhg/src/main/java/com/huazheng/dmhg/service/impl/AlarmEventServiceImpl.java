package com.huazheng.dmhg.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.huazheng.dmhg.common.R;
import com.huazheng.dmhg.dto.AlarmEventReceiveDTO;
import com.huazheng.dmhg.entity.AlarmEvent;
import com.huazheng.dmhg.mapper.AlarmEventMapper;
import com.huazheng.dmhg.service.AlarmEventService;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 告警记录表 Service 实现类
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
@Slf4j
@Service
public class AlarmEventServiceImpl extends ServiceImpl<AlarmEventMapper, AlarmEvent> implements AlarmEventService {

    @Autowired
    private AlarmEventMapper alarmEventMapper;

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 告警记录分页查询
     *
     * @param alarmEvent 查询条件
     * @return 分页结果
     * <AUTHOR>
     * @since 2025-08-20
     */
    @Override
    public Page<AlarmEvent> page(AlarmEvent alarmEvent) {
        Page<AlarmEvent> page = new Page<>(alarmEvent.getPage() == null ? 1 : alarmEvent.getPage(),
                alarmEvent.getLimit() == null ? 10 : alarmEvent.getLimit());
        List<AlarmEvent> records = alarmEventMapper.pageAlarmEvent(page, alarmEvent);
        page.setRecords(records);
        return page;
    }

    /**
     * 告警记录详情
     *
     * @param alertId 告警ID
     * @return 告警详情
     * <AUTHOR>
     * @since 2025-08-20
     */
    @Override
    public R<AlarmEvent> info(Long alertId) {
        AlarmEvent alarmEvent = alarmEventMapper.infoAlarmEvent(alertId);
        if (alarmEvent == null) {
            return R.error("告警记录不存在");
        }
        return R.success(alarmEvent);
    }

    /**
     * 保存告警记录
     *
     * @param alarmEvent 告警记录
     * @return 保存结果
     * <AUTHOR>
     * @since 2025-08-20
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R saveAlarmEvent(AlarmEvent alarmEvent) {
        // 简化版本：使用默认用户信息
        alarmEvent.setCreateBy(1L);
        alarmEvent.setCreateName("系统管理员");
        alarmEvent.setCreateTime(LocalDateTime.now());
        alarmEvent.setDelFlag("N");

        int result = alarmEventMapper.saveAlarmEvent(alarmEvent);
        if (result > 0) {
            return R.success(alarmEvent);
        }
        return R.error("保存失败");
    }

    /**
     * 修改告警记录
     *
     * @param alarmEvent 告警记录
     * @return 修改结果
     * <AUTHOR>
     * @since 2025-08-20
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Void> update(AlarmEvent alarmEvent) {
        // 简化版本：使用默认用户信息
        alarmEvent.setUpdateBy(1L);
        alarmEvent.setUpdateName("系统管理员");
        alarmEvent.setUpdateTime(LocalDateTime.now());

        int result = alarmEventMapper.updateAlarmEvent(alarmEvent);
        if (result > 0) {
            return R.success();
        }
        return R.error("修改失败");
    }

    /**
     * 批量删除告警记录
     *
     * @param alertIds 告警ID数组
     * @return 删除结果
     * <AUTHOR>
     * @since 2025-08-20
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Void> delete(Long[] alertIds) {
        // 简化版本：使用默认用户信息
        int result = alarmEventMapper.deleteAlarmEvent(alertIds, 1L);
        if (result > 0) {
            return R.success();
        }
        return R.error("删除失败");
    }

    /**
     * 告警记录列表查询
     *
     * @param alarmEvent 查询条件
     * @return 告警列表
     * <AUTHOR>
     * @since 2025-08-20
     */
    @Override
    public R<List<AlarmEvent>> list(AlarmEvent alarmEvent) {
        List<AlarmEvent> list = alarmEventMapper.listAlarmEvent(alarmEvent);
        return R.success(list);
    }

    /**
     * 接收对接方告警事件数据
     *
     * @param receiveDTO 对接方告警事件数据
     * @return 保存结果
     * <AUTHOR>
     * @since 2025-08-20
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<AlarmEvent> receiveAlarmEvent(AlarmEventReceiveDTO receiveDTO) {
        try {
            // 创建告警事件对象
            AlarmEvent alarmEvent = new AlarmEvent();

            // 基本信息映射
            alarmEvent.setDeviceId(receiveDTO.getDeviceId());
            alarmEvent.setDeviceName(receiveDTO.getAlias()); // 使用别名作为设备名称
            alarmEvent.setAlertCategory(receiveDTO.getLabel());
            alarmEvent.setAlertImage(receiveDTO.getImgBase64());

            // 时间转换：优先使用timestamp，如果没有则使用当前时间
            LocalDateTime reportTime;
            if (receiveDTO.getTimestamp() != null) {
                reportTime = LocalDateTime.ofInstant(
                        Instant.ofEpochSecond(receiveDTO.getTimestamp()),
                        ZoneId.systemDefault()
                );
            } else {
                reportTime = LocalDateTime.now();
            }
            alarmEvent.setReportTime(reportTime);

            // 构建扩展信息
            Map<String, Object> extendInfoMap = new HashMap<>();
            extendInfoMap.put("deviceVersion", receiveDTO.getDeviceVersion());
            extendInfoMap.put("date", receiveDTO.getDate());
            extendInfoMap.put("timestamp", receiveDTO.getTimestamp());
            extendInfoMap.put("alias", receiveDTO.getAlias());
            extendInfoMap.put("count", receiveDTO.getCount());

            // 如果有原始扩展信息，也加入
            if (receiveDTO.getExtend() != null) {
                extendInfoMap.put("originalExtend", receiveDTO.getExtend());
            }

            // 转换为JSON字符串
            String extendInfoJson = objectMapper.writeValueAsString(extendInfoMap);
            alarmEvent.setExtendInfo(extendInfoJson);

            // 设置默认状态
            alarmEvent.setEventStatus("待处理");

            // 设置系统字段
            alarmEvent.setCreateBy(0L); // 系统自动创建
            alarmEvent.setCreateName("系统");
            alarmEvent.setCreateTime(LocalDateTime.now());
            alarmEvent.setDelFlag("N");

            // 保存到数据库
            int result = alarmEventMapper.saveAlarmEvent(alarmEvent);
            if (result > 0) {
                log.info("成功接收告警事件，设备ID：{}，告警类别：{}", receiveDTO.getDeviceId(), receiveDTO.getLabel());
                return R.success(alarmEvent);
            }
            return R.error("保存告警事件失败");

        } catch (Exception e) {
            log.error("接收告警事件异常：", e);
            return R.error("接收告警事件异常：" + e.getMessage());
        }
    }

    @Override
    public R handle(AlarmEvent alarmEvent) {
        if (alarmEvent.getAlertId() == null) {
            return R.error("告警ID不能为空");
        }
        AlarmEvent info = alarmEventMapper.infoAlarmEvent(alarmEvent.getAlertId());
        if (info == null) {
            return R.error("告警不存在");
        }
        if (StrUtil.isBlank(info.getEventStatus()) || "待处理".equals(info.getEventStatus())) {
            return R.error("处置状态错误！");
        }
        alarmEvent.setHandler("系统管理员");
        alarmEvent.setHandleTime(LocalDateTime.now());
        alarmEventMapper.updateAlarmEvent(alarmEvent);
        return R.success();
    }

}
