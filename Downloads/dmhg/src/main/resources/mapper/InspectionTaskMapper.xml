<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.huazheng.dmhg.mapper.InspectionTaskMapper">
    <!-- 通用返回对象 -->
    <resultMap type="com.huazheng.dmhg.entity.InspectionTask" id="inspectionTaskResult">
        <result property="taskId" column="task_id"/> <!-- 任务ID -->
        <result property="taskName" column="task_name"/> <!-- 任务名称 -->
        <result property="executeId" column="execute_id"/> <!-- 机器狗ID/巡更组ID/视频设备组ID -->
        <result property="startTime" column="start_time"/> <!-- 开始时间 -->
        <result property="endTime" column="end_time"/> <!-- 结束时间 -->
        <result property="taskStatus" column="task_status"/> <!-- 任务状态 -->
        <result property="taskType" column="task_type"/> <!-- 任务类型 -->
        <result property="delFlag" column="del_flag"/> <!-- 删除标志 -->
        <result property="remark" column="remark"/> <!-- 备注 -->
        <result property="createBy" column="create_by"/> <!-- 创建人ID -->
        <result property="createName" column="create_name"/> <!-- 创建人姓名 -->
        <result property="createTime" column="create_time"/> <!-- 创建时间 -->
        <result property="updateBy" column="update_by"/> <!-- 修改人ID -->
        <result property="updateName" column="update_name"/> <!-- 修改人 -->
        <result property="updateTime" column="update_time"/> <!-- 修改时间 -->
    </resultMap>

    <sql id="selectInspectionTaskVo">
        select task_id,
               task_name,
               execute_id,
               start_time,
               end_time,
               task_status,
               task_type,
               del_flag,
               remark,
               create_by,
               create_name,
               create_time,
               update_by,
               update_name,
               update_time
        from inspection_task
    </sql>

    <!-- 模糊查询条件 -->
    <sql id="like">
        <if test="taskName != null and taskName != ''">
            AND task_name like concat('%', #{taskName}, '%')
        </if>
        <if test="executeId != null and executeId != ''">
            AND execute_id = #{executeId}
        </if>
        <if test="taskStatus != null and taskStatus != ''">
            AND task_status = #{taskStatus}
        </if>
        <if test="taskType != null and taskType != ''">
            AND task_type = #{taskType}
        </if>
        <if test="queryStartTime != null and queryStartTime != ''">
            AND start_time >= #{queryStartTime}
        </if>
        <if test="queryEndTime != null and queryEndTime != ''">
            AND end_time &lt;= #{queryEndTime}
        </if>
        AND del_flag = 'N'
    </sql>

    <!-- 分页查询巡检任务 -->
    <select id="pageInspectionTask" parameterType="InspectionTask" resultMap="inspectionTaskResult">
        <include refid="selectInspectionTaskVo"/>
        <where>
            <include refid="like"/>
        </where>
        ORDER BY create_time DESC
    </select>

    <!-- 根据主键查询对象 -->
    <select id="infoInspectionTask" parameterType="Long" resultMap="inspectionTaskResult">
        <include refid="selectInspectionTaskVo"/>
        WHERE task_id = #{taskId} AND del_flag = 'N'
    </select>

    <!-- 保存巡检任务 -->
    <insert id="saveInspectionTask" parameterType="InspectionTask" useGeneratedKeys="true" keyProperty="taskId">
        INSERT INTO inspection_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskName != null and taskName != ''">
                task_name,
            </if>
            <if test="executeId != null and executeId != ''">
                execute_id,
            </if>
            <if test="startTime != null">
                start_time,
            </if>
            <if test="endTime != null">
                end_time,
            </if>
            <if test="taskStatus != null and taskStatus != ''">
                task_status,
            </if>
            <if test="taskType != null and taskType != ''">
                task_type,
            </if>
            <if test="delFlag != null and delFlag != ''">
                del_flag,
            </if>
            <if test="remark != null and remark != ''">
                remark,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createName != null and createName != ''">
                create_name,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskName != null and taskName != ''">
                #{taskName},
            </if>
            <if test="executeId != null and executeId != ''">
                #{executeId},
            </if>
            <if test="startTime != null">
                #{startTime},
            </if>
            <if test="endTime != null">
                #{endTime},
            </if>
            <if test="taskStatus != null and taskStatus != ''">
                #{taskStatus},
            </if>
            <if test="taskType != null and taskType != ''">
                #{taskType},
            </if>
            <if test="delFlag != null and delFlag != ''">
                #{delFlag},
            </if>
            <if test="remark != null and remark != ''">
                #{remark},
            </if>
            <if test="createBy != null">
                #{createBy},
            </if>
            <if test="createName != null and createName != ''">
                #{createName},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
        </trim>
    </insert>

    <!-- 修改巡检任务 -->
    <update id="updateInspectionTask" parameterType="InspectionTask">
        UPDATE inspection_task
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskName != null and taskName != ''">task_name = #{taskName},</if>
            <if test="executeId != null and executeId != ''">execute_id = #{executeId},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="taskStatus != null and taskStatus != ''">task_status = #{taskStatus},</if>
            <if test="taskType != null and taskType != ''">task_type = #{taskType},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateName != null and updateName != ''">update_name = #{updateName},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        WHERE task_id = #{taskId} AND del_flag = 'N'
    </update>

    <!-- 批量删除巡检任务（逻辑删除） -->
    <update id="deleteInspectionTask">
        UPDATE inspection_task SET del_flag = 'Y', update_by = #{updateBy}, update_time = NOW()
        WHERE task_id IN
        <foreach item="taskId" collection="taskIds" open="(" separator="," close=")">
            #{taskId}
        </foreach>
        AND del_flag = 'N'
    </update>

    <!-- 查询巡检任务列表 -->
    <select id="listInspectionTask" parameterType="InspectionTask" resultMap="inspectionTaskResult">
        <include refid="selectInspectionTaskVo"/>
        <where>
            <include refid="like"/>
        </where>
        ORDER BY create_time DESC
    </select>

</mapper>
