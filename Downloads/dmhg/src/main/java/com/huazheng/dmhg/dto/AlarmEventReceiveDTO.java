package com.huazheng.dmhg.dto;

import lombok.Data;

/**
 * 接收对接方告警事件数据的DTO
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
@Data
public class AlarmEventReceiveDTO {

    /**
     * 设备号
     */
    private String deviceId;

    /**
     * 设备版本
     */
    private String deviceVersion;

    /**
     * 告警上报的日期
     */
    private String date;

    /**
     * 告警上报的UTC时间戳
     */
    private Long timestamp;

    /**
     * 告警类别
     */
    private String label;

    /**
     * 类别别名
     */
    private String alias;

    /**
     * 检测计数
     */
    private Integer count;

    /**
     * 告警图片（BASE64格式）
     */
    private String imgBase64;

    /**
     * 告警扩展信息
     */
    private Object extend;

}
