package com.huazheng.dmhg.mapper;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.huazheng.dmhg.entity.AlarmEvent;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 告警记录表 Mapper
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
@Component
public interface AlarmEventMapper extends BaseMapper<AlarmEvent> {

    /**
     * 分页查询告警记录
     *
     * @param page  分页参数
     * @param param 查询参数
     * @return 告警记录列表
     * <AUTHOR>
     * @since 2025-08-20
     */
    List<AlarmEvent> pageAlarmEvent(Page<AlarmEvent> page, AlarmEvent param);

    /**
     * 查询告警记录详情
     *
     * @param alertId 告警ID
     * @return 告警记录详情
     * <AUTHOR>
     * @since 2025-08-20
     */
    AlarmEvent infoAlarmEvent(Long alertId);

    /**
     * 保存告警记录
     *
     * @param param 告警记录参数
     * @return 影响行数
     * <AUTHOR>
     * @since 2025-08-20
     */
    int saveAlarmEvent(AlarmEvent param);

    /**
     * 修改告警记录
     *
     * @param param 告警记录参数
     * @return 影响行数
     * <AUTHOR>
     * @since 2025-08-20
     */
    int updateAlarmEvent(AlarmEvent param);

    /**
     * 批量删除告警记录
     *
     * @param alertIds 告警ID数组
     * @param updateBy 修改人ID
     * @return 影响行数
     * <AUTHOR>
     * @since 2025-08-20
     */
    int deleteAlarmEvent(@Param("alertIds") Long[] alertIds, @Param("updateBy") Long updateBy);

    /**
     * 查询告警记录列表
     *
     * @param alarmEvent 查询参数
     * @return 告警记录列表
     * <AUTHOR>
     * @since 2025-08-20
     */
    List<AlarmEvent> listAlarmEvent(AlarmEvent alarmEvent);

}
